package com.example.l4lb;

import com.example.l4lb.config.LoadBalancerConfig;
import com.example.l4lb.server.LoadBalancerServer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;

/**
 * Main application class for the L4 Load Balancer.
 * 
 * This application provides Layer 4 (Transport Layer) load balancing capabilities
 * using Netty for high-performance networking and Spring Boot for configuration
 * and dependency management.
 */
@SpringBootApplication
@EnableConfigurationProperties(LoadBalancerConfig.class)
public class LoadBalancerApplication implements CommandLineRunner {

    private static final Logger logger = LoggerFactory.getLogger(LoadBalancerApplication.class);

    @Autowired
    private LoadBalancerServer loadBalancerServer;

    @Autowired
    private LoadBalancerConfig config;

    public static void main(String[] args) {
        System.setProperty("spring.application.name", "l4-load-balancer");
        
        // Set JVM options for better performance
        System.setProperty("io.netty.leakDetection.level", "SIMPLE");
        System.setProperty("io.netty.allocator.type", "pooled");
        
        logger.info("Starting L4 Load Balancer Application...");
        
        try {
            SpringApplication app = new SpringApplication(LoadBalancerApplication.class);
            app.run(args);
        } catch (Exception e) {
            logger.error("Failed to start L4 Load Balancer Application", e);
            System.exit(1);
        }
    }

    @Override
    public void run(String... args) throws Exception {
        logger.info("Initializing L4 Load Balancer with configuration: {}", config);
        
        // Add shutdown hook for graceful shutdown
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            logger.info("Shutting down L4 Load Balancer...");
            try {
                loadBalancerServer.stop();
                logger.info("L4 Load Balancer stopped successfully");
            } catch (Exception e) {
                logger.error("Error during shutdown", e);
            }
        }));

        // Start the load balancer server
        try {
            loadBalancerServer.start();
            logger.info("L4 Load Balancer started successfully on port {}", config.getPort());
            
            // Keep the application running
            Thread.currentThread().join();
        } catch (InterruptedException e) {
            logger.info("Application interrupted, shutting down...");
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            logger.error("Failed to start load balancer server", e);
            throw e;
        }
    }
}
