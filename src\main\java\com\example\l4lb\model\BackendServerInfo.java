package com.example.l4lb.model;

import java.util.Objects;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Information about a backend server including connection statistics.
 */
public class BackendServerInfo {

    private final String host;
    private final int port;
    private final int weight;
    private volatile boolean enabled;
    private volatile boolean healthy;

    // Connection statistics
    private final AtomicLong totalConnections = new AtomicLong(0);
    private final AtomicLong activeConnections = new AtomicLong(0);
    private final AtomicLong failedConnections = new AtomicLong(0);

    // Health check statistics
    private volatile long lastHealthCheckTime;
    private volatile boolean lastHealthCheckResult;
    private final AtomicLong consecutiveFailures = new AtomicLong(0);

    public BackendServerInfo(String host, int port, int weight, boolean enabled) {
        this.host = host;
        this.port = port;
        this.weight = weight;
        this.enabled = enabled;
        this.healthy = enabled; // Initially healthy if enabled
        this.lastHealthCheckTime = System.currentTimeMillis();
        this.lastHealthCheckResult = enabled;
    }

    // Basic getters
    public String getHost() {
        return host;
    }

    public int getPort() {
        return port;
    }

    public int getWeight() {
        return weight;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public boolean isHealthy() {
        return healthy && enabled;
    }

    public void setHealthy(boolean healthy) {
        this.healthy = healthy;
        this.lastHealthCheckTime = System.currentTimeMillis();
        this.lastHealthCheckResult = healthy;
        
        if (healthy) {
            consecutiveFailures.set(0);
        } else {
            consecutiveFailures.incrementAndGet();
        }
    }

    // Connection statistics
    public long getTotalConnections() {
        return totalConnections.get();
    }

    public long getActiveConnections() {
        return activeConnections.get();
    }

    public long getFailedConnections() {
        return failedConnections.get();
    }

    public void incrementActiveConnections() {
        totalConnections.incrementAndGet();
        activeConnections.incrementAndGet();
    }

    public void decrementActiveConnections() {
        activeConnections.decrementAndGet();
    }

    public void incrementFailedConnections() {
        failedConnections.incrementAndGet();
    }

    // Health check statistics
    public long getLastHealthCheckTime() {
        return lastHealthCheckTime;
    }

    public boolean getLastHealthCheckResult() {
        return lastHealthCheckResult;
    }

    public long getConsecutiveFailures() {
        return consecutiveFailures.get();
    }

    /**
     * Get the server address as "host:port".
     */
    public String getAddress() {
        return host + ":" + port;
    }

    /**
     * Get connection statistics.
     */
    public ConnectionStats getConnectionStats() {
        return new ConnectionStats(
                totalConnections.get(),
                activeConnections.get(),
                failedConnections.get()
        );
    }

    /**
     * Get health statistics.
     */
    public HealthStats getHealthStats() {
        return new HealthStats(
                healthy,
                lastHealthCheckTime,
                lastHealthCheckResult,
                consecutiveFailures.get()
        );
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        BackendServerInfo that = (BackendServerInfo) o;
        return port == that.port && Objects.equals(host, that.host);
    }

    @Override
    public int hashCode() {
        return Objects.hash(host, port);
    }

    @Override
    public String toString() {
        return "BackendServerInfo{" +
                "host='" + host + '\'' +
                ", port=" + port +
                ", weight=" + weight +
                ", enabled=" + enabled +
                ", healthy=" + healthy +
                ", activeConnections=" + activeConnections.get() +
                ", totalConnections=" + totalConnections.get() +
                '}';
    }

    /**
     * Connection statistics data class.
     */
    public static class ConnectionStats {
        private final long total;
        private final long active;
        private final long failed;

        public ConnectionStats(long total, long active, long failed) {
            this.total = total;
            this.active = active;
            this.failed = failed;
        }

        public long getTotal() { return total; }
        public long getActive() { return active; }
        public long getFailed() { return failed; }

        @Override
        public String toString() {
            return "ConnectionStats{" +
                    "total=" + total +
                    ", active=" + active +
                    ", failed=" + failed +
                    '}';
        }
    }

    /**
     * Health statistics data class.
     */
    public static class HealthStats {
        private final boolean healthy;
        private final long lastCheckTime;
        private final boolean lastCheckResult;
        private final long consecutiveFailures;

        public HealthStats(boolean healthy, long lastCheckTime, boolean lastCheckResult, long consecutiveFailures) {
            this.healthy = healthy;
            this.lastCheckTime = lastCheckTime;
            this.lastCheckResult = lastCheckResult;
            this.consecutiveFailures = consecutiveFailures;
        }

        public boolean isHealthy() { return healthy; }
        public long getLastCheckTime() { return lastCheckTime; }
        public boolean getLastCheckResult() { return lastCheckResult; }
        public long getConsecutiveFailures() { return consecutiveFailures; }

        @Override
        public String toString() {
            return "HealthStats{" +
                    "healthy=" + healthy +
                    ", lastCheckTime=" + lastCheckTime +
                    ", lastCheckResult=" + lastCheckResult +
                    ", consecutiveFailures=" + consecutiveFailures +
                    '}';
        }
    }
}
