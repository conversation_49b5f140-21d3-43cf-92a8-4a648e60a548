package com.example.l4lb.server;

import com.example.l4lb.config.LoadBalancerConfig;
import com.example.l4lb.handler.LoadBalancerHandler;
import com.example.l4lb.loadbalancing.LoadBalancer;
import com.example.l4lb.metrics.MetricsCollector;
import io.netty.bootstrap.ServerBootstrap;
import io.netty.channel.*;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.SocketChannel;
import io.netty.channel.socket.nio.NioServerSocketChannel;
import io.netty.handler.logging.LogLevel;
import io.netty.handler.logging.LoggingHandler;
import io.netty.handler.timeout.IdleStateHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * Main load balancer server using Netty for high-performance networking.
 * 
 * This server accepts incoming TCP connections and forwards them to backend servers
 * based on the configured load balancing algorithm.
 */
@Component
public class LoadBalancerServer {

    private static final Logger logger = LoggerFactory.getLogger(LoadBalancerServer.class);

    private final LoadBalancerConfig config;
    private final LoadBalancer loadBalancer;
    private final MetricsCollector metricsCollector;

    private EventLoopGroup bossGroup;
    private EventLoopGroup workerGroup;
    private Channel serverChannel;

    @Autowired
    public LoadBalancerServer(LoadBalancerConfig config, 
                             LoadBalancer loadBalancer,
                             MetricsCollector metricsCollector) {
        this.config = config;
        this.loadBalancer = loadBalancer;
        this.metricsCollector = metricsCollector;
    }

    /**
     * Start the load balancer server.
     */
    public void start() throws InterruptedException {
        logger.info("Starting Load Balancer Server on port {}", config.getPort());

        // Create event loop groups
        bossGroup = new NioEventLoopGroup(1); // Single thread for accepting connections
        workerGroup = new NioEventLoopGroup(); // Default number of threads for handling connections

        try {
            ServerBootstrap bootstrap = new ServerBootstrap();
            bootstrap.group(bossGroup, workerGroup)
                    .channel(NioServerSocketChannel.class)
                    .option(ChannelOption.SO_BACKLOG, 1024)
                    .option(ChannelOption.SO_REUSEADDR, true)
                    .childOption(ChannelOption.SO_KEEPALIVE, config.getConnection().isKeepAlive())
                    .childOption(ChannelOption.TCP_NODELAY, config.getConnection().isTcpNoDelay())
                    .childOption(ChannelOption.CONNECT_TIMEOUT_MILLIS, (int) config.getConnection().getConnectionTimeoutMs())
                    .handler(new LoggingHandler(LogLevel.INFO))
                    .childHandler(new ChannelInitializer<SocketChannel>() {
                        @Override
                        protected void initChannel(SocketChannel ch) throws Exception {
                            ChannelPipeline pipeline = ch.pipeline();
                            
                            // Add idle state handler for connection timeout
                            pipeline.addLast(new IdleStateHandler(
                                    0, 0, config.getConnection().getIdleTimeoutMs(), TimeUnit.MILLISECONDS));
                            
                            // Add the main load balancer handler
                            pipeline.addLast(new LoadBalancerHandler(loadBalancer, metricsCollector, config));
                        }
                    });

            // Bind and start to accept incoming connections
            ChannelFuture future = bootstrap.bind(config.getPort()).sync();
            serverChannel = future.channel();
            
            logger.info("Load Balancer Server started successfully on port {}", config.getPort());
            
            // Log backend servers
            config.getBackends().forEach(backend -> 
                logger.info("Backend server configured: {}:{} (weight: {}, enabled: {})", 
                           backend.getHost(), backend.getPort(), backend.getWeight(), backend.isEnabled()));

        } catch (Exception e) {
            logger.error("Failed to start Load Balancer Server", e);
            stop();
            throw e;
        }
    }

    /**
     * Stop the load balancer server gracefully.
     */
    public void stop() {
        logger.info("Stopping Load Balancer Server...");

        try {
            // Close server channel
            if (serverChannel != null) {
                serverChannel.close().sync();
            }
        } catch (InterruptedException e) {
            logger.warn("Interrupted while closing server channel", e);
            Thread.currentThread().interrupt();
        }

        // Shutdown event loop groups
        if (bossGroup != null) {
            bossGroup.shutdownGracefully(2, 15, TimeUnit.SECONDS);
        }
        if (workerGroup != null) {
            workerGroup.shutdownGracefully(2, 15, TimeUnit.SECONDS);
        }

        logger.info("Load Balancer Server stopped");
    }

    /**
     * Check if the server is running.
     */
    public boolean isRunning() {
        return serverChannel != null && serverChannel.isActive();
    }

    /**
     * Get server statistics.
     */
    public ServerStats getStats() {
        return new ServerStats(
                isRunning(),
                config.getPort(),
                config.getBackends().size(),
                loadBalancer.getActiveBackendCount(),
                metricsCollector.getTotalConnections(),
                metricsCollector.getActiveConnections()
        );
    }

    /**
     * Server statistics data class.
     */
    public static class ServerStats {
        private final boolean running;
        private final int port;
        private final int totalBackends;
        private final int activeBackends;
        private final long totalConnections;
        private final long activeConnections;

        public ServerStats(boolean running, int port, int totalBackends, int activeBackends, 
                          long totalConnections, long activeConnections) {
            this.running = running;
            this.port = port;
            this.totalBackends = totalBackends;
            this.activeBackends = activeBackends;
            this.totalConnections = totalConnections;
            this.activeConnections = activeConnections;
        }

        // Getters
        public boolean isRunning() { return running; }
        public int getPort() { return port; }
        public int getTotalBackends() { return totalBackends; }
        public int getActiveBackends() { return activeBackends; }
        public long getTotalConnections() { return totalConnections; }
        public long getActiveConnections() { return activeConnections; }

        @Override
        public String toString() {
            return "ServerStats{" +
                    "running=" + running +
                    ", port=" + port +
                    ", totalBackends=" + totalBackends +
                    ", activeBackends=" + activeBackends +
                    ", totalConnections=" + totalConnections +
                    ", activeConnections=" + activeConnections +
                    '}';
        }
    }
}
