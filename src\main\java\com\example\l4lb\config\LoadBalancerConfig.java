package com.example.l4lb.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * Configuration properties for the L4 Load Balancer.
 */
@ConfigurationProperties(prefix = "loadbalancer")
@Validated
public class LoadBalancerConfig {

    /**
     * Port on which the load balancer listens for incoming connections.
     * Port 0 means use a random available port (useful for testing).
     */
    @Min(0)
    @Max(65535)
    private int port = 8080;

    /**
     * Load balancing algorithm to use.
     */
    @NotNull
    private LoadBalancingAlgorithm algorithm = LoadBalancingAlgorithm.ROUND_ROBIN;

    /**
     * List of backend servers.
     */
    @NotEmpty
    @Valid
    private List<BackendServer> backends;

    /**
     * Health check configuration.
     */
    @Valid
    private HealthCheckConfig healthCheck = new HealthCheckConfig();

    /**
     * Connection configuration.
     */
    @Valid
    private ConnectionConfig connection = new ConnectionConfig();

    /**
     * Metrics configuration.
     */
    @Valid
    private MetricsConfig metrics = new MetricsConfig();

    // Getters and setters
    public int getPort() {
        return port;
    }

    public void setPort(int port) {
        this.port = port;
    }

    public LoadBalancingAlgorithm getAlgorithm() {
        return algorithm;
    }

    public void setAlgorithm(LoadBalancingAlgorithm algorithm) {
        this.algorithm = algorithm;
    }

    public List<BackendServer> getBackends() {
        return backends;
    }

    public void setBackends(List<BackendServer> backends) {
        this.backends = backends;
    }

    public HealthCheckConfig getHealthCheck() {
        return healthCheck;
    }

    public void setHealthCheck(HealthCheckConfig healthCheck) {
        this.healthCheck = healthCheck;
    }

    public ConnectionConfig getConnection() {
        return connection;
    }

    public void setConnection(ConnectionConfig connection) {
        this.connection = connection;
    }

    public MetricsConfig getMetrics() {
        return metrics;
    }

    public void setMetrics(MetricsConfig metrics) {
        this.metrics = metrics;
    }

    @Override
    public String toString() {
        return "LoadBalancerConfig{" +
                "port=" + port +
                ", algorithm=" + algorithm +
                ", backends=" + backends +
                ", healthCheck=" + healthCheck +
                ", connection=" + connection +
                ", metrics=" + metrics +
                '}';
    }

    /**
     * Load balancing algorithms supported by the load balancer.
     */
    public enum LoadBalancingAlgorithm {
        ROUND_ROBIN,
        LEAST_CONNECTIONS,
        WEIGHTED_ROUND_ROBIN,
        IP_HASH,
        RANDOM
    }

    /**
     * Backend server configuration.
     */
    public static class BackendServer {
        @NotEmpty
        private String host;

        @Min(1)
        @Max(65535)
        private int port;

        @Min(1)
        private int weight = 1;

        private boolean enabled = true;

        // Getters and setters
        public String getHost() {
            return host;
        }

        public void setHost(String host) {
            this.host = host;
        }

        public int getPort() {
            return port;
        }

        public void setPort(int port) {
            this.port = port;
        }

        public int getWeight() {
            return weight;
        }

        public void setWeight(int weight) {
            this.weight = weight;
        }

        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }

        @Override
        public String toString() {
            return "BackendServer{" +
                    "host='" + host + '\'' +
                    ", port=" + port +
                    ", weight=" + weight +
                    ", enabled=" + enabled +
                    '}';
        }
    }

    /**
     * Health check configuration.
     */
    public static class HealthCheckConfig {
        private boolean enabled = true;
        private long intervalSeconds = 30;
        private long timeoutSeconds = 5;
        private int maxRetries = 3;
        private String path = "/health";

        // Getters and setters
        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }

        public long getIntervalSeconds() {
            return intervalSeconds;
        }

        public void setIntervalSeconds(long intervalSeconds) {
            this.intervalSeconds = intervalSeconds;
        }

        public long getTimeoutSeconds() {
            return timeoutSeconds;
        }

        public void setTimeoutSeconds(long timeoutSeconds) {
            this.timeoutSeconds = timeoutSeconds;
        }

        public int getMaxRetries() {
            return maxRetries;
        }

        public void setMaxRetries(int maxRetries) {
            this.maxRetries = maxRetries;
        }

        public String getPath() {
            return path;
        }

        public void setPath(String path) {
            this.path = path;
        }

        @Override
        public String toString() {
            return "HealthCheckConfig{" +
                    "enabled=" + enabled +
                    ", intervalSeconds=" + intervalSeconds +
                    ", timeoutSeconds=" + timeoutSeconds +
                    ", maxRetries=" + maxRetries +
                    ", path='" + path + '\'' +
                    '}';
        }
    }

    /**
     * Connection configuration.
     */
    public static class ConnectionConfig {
        private int maxConnections = 1000;
        private long connectionTimeoutMs = 5000;
        private long idleTimeoutMs = 300000; // 5 minutes
        private boolean keepAlive = true;
        private boolean tcpNoDelay = true;

        // Getters and setters
        public int getMaxConnections() {
            return maxConnections;
        }

        public void setMaxConnections(int maxConnections) {
            this.maxConnections = maxConnections;
        }

        public long getConnectionTimeoutMs() {
            return connectionTimeoutMs;
        }

        public void setConnectionTimeoutMs(long connectionTimeoutMs) {
            this.connectionTimeoutMs = connectionTimeoutMs;
        }

        public long getIdleTimeoutMs() {
            return idleTimeoutMs;
        }

        public void setIdleTimeoutMs(long idleTimeoutMs) {
            this.idleTimeoutMs = idleTimeoutMs;
        }

        public boolean isKeepAlive() {
            return keepAlive;
        }

        public void setKeepAlive(boolean keepAlive) {
            this.keepAlive = keepAlive;
        }

        public boolean isTcpNoDelay() {
            return tcpNoDelay;
        }

        public void setTcpNoDelay(boolean tcpNoDelay) {
            this.tcpNoDelay = tcpNoDelay;
        }

        @Override
        public String toString() {
            return "ConnectionConfig{" +
                    "maxConnections=" + maxConnections +
                    ", connectionTimeoutMs=" + connectionTimeoutMs +
                    ", idleTimeoutMs=" + idleTimeoutMs +
                    ", keepAlive=" + keepAlive +
                    ", tcpNoDelay=" + tcpNoDelay +
                    '}';
        }
    }

    /**
     * Metrics configuration.
     */
    public static class MetricsConfig {
        private boolean enabled = true;
        private int port = 9090;
        private String path = "/metrics";

        // Getters and setters
        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }

        public int getPort() {
            return port;
        }

        public void setPort(int port) {
            this.port = port;
        }

        public String getPath() {
            return path;
        }

        public void setPath(String path) {
            this.path = path;
        }

        @Override
        public String toString() {
            return "MetricsConfig{" +
                    "enabled=" + enabled +
                    ", port=" + port +
                    ", path='" + path + '\'' +
                    '}';
        }
    }
}
