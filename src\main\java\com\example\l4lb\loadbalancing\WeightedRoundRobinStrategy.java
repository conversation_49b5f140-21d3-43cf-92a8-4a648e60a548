package com.example.l4lb.loadbalancing;

import com.example.l4lb.model.BackendServerInfo;

import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Weighted round-robin load balancing strategy.
 * Distributes requests based on the weight assigned to each backend server.
 */
public class WeightedRoundRobinStrategy implements LoadBalancingStrategy {

    private final AtomicInteger currentWeight = new AtomicInteger(0);

    @Override
    public BackendServerInfo selectBackend(List<BackendServerInfo> backends, String clientAddress) {
        if (backends == null || backends.isEmpty()) {
            return null;
        }

        // Calculate total weight
        int totalWeight = backends.stream()
                .mapToInt(BackendServerInfo::getWeight)
                .sum();

        if (totalWeight == 0) {
            // Fallback to round-robin if all weights are 0
            return backends.get(currentWeight.getAndIncrement() % backends.size());
        }

        // Get current weight position
        int current = currentWeight.getAndIncrement() % totalWeight;
        int weightSum = 0;

        // Find the backend that corresponds to the current weight position
        for (BackendServerInfo backend : backends) {
            weightSum += backend.getWeight();
            if (current < weightSum) {
                return backend;
            }
        }

        // Fallback (should not happen)
        return backends.get(0);
    }
}
