version: '3.8'

services:
  # L4 Load Balancer
  load-balancer:
    build: .
    container_name: l4-load-balancer
    ports:
      - "8080:8080"  # Load balancer port
      - "9090:9090"  # Metrics port
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - JAVA_OPTS=-Xms512m -Xmx1g -XX:+UseG1GC
    volumes:
      - ./config:/app/config:ro
      - ./logs:/app/logs
    networks:
      - lb-network
    depends_on:
      - backend1
      - backend2
      - backend3
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9090/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Mock Backend Servers for testing
  backend1:
    image: nginx:alpine
    container_name: backend1
    ports:
      - "8081:80"
    volumes:
      - ./test-data/backend1:/usr/share/nginx/html:ro
    networks:
      - lb-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  backend2:
    image: nginx:alpine
    container_name: backend2
    ports:
      - "8082:80"
    volumes:
      - ./test-data/backend2:/usr/share/nginx/html:ro
    networks:
      - lb-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  backend3:
    image: nginx:alpine
    container_name: backend3
    ports:
      - "8083:80"
    volumes:
      - ./test-data/backend3:/usr/share/nginx/html:ro
    networks:
      - lb-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Prometheus for metrics collection
  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus
    ports:
      - "9091:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - lb-network
    restart: unless-stopped

  # Grafana for metrics visualization
  grafana:
    image: grafana/grafana:latest
    container_name: grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana-data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    networks:
      - lb-network
    restart: unless-stopped
    depends_on:
      - prometheus

networks:
  lb-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  prometheus-data:
  grafana-data:
