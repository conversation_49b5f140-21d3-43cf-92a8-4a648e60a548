# L4 Load Balancer Configuration

# Spring Boot Configuration
spring:
  application:
    name: l4-load-balancer
  profiles:
    active: dev

# Load Balancer Configuration
loadbalancer:
  # Port on which the load balancer listens
  port: 8080
  
  # Load balancing algorithm
  # Options: ROUND_ROBIN, LEAST_CONNECTIONS, WEIGHTED_ROUND_ROBIN, IP_HASH, RANDOM
  algorithm: ROUND_ROBIN
  
  # Backend servers (your REST API instances)
  backends:
    - host: localhost
      port: 8081  # First REST API instance
      weight: 1
      enabled: true
    - host: localhost
      port: 8082  # Second REST API instance
      weight: 1
      enabled: true
  
  # Health check configuration
  health-check:
    enabled: true
    interval-seconds: 30
    timeout-seconds: 5
    max-retries: 3
    path: /health
  
  # Connection configuration
  connection:
    max-connections: 1000
    connection-timeout-ms: 5000
    idle-timeout-ms: 300000  # 5 minutes
    keep-alive: true
    tcp-no-delay: true
  
  # Metrics configuration
  metrics:
    enabled: true
    port: 9090
    path: /metrics

# Management endpoints (Spring Boot Actuator)
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true

# Logging configuration
logging:
  level:
    com.example.l4lb: INFO
    io.netty: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/l4-load-balancer.log

---
# Development profile
spring:
  config:
    activate:
      on-profile: dev

logging:
  level:
    com.example.l4lb: DEBUG
    root: INFO

loadbalancer:
  backends:
    - host: localhost
      port: 8081
      weight: 1
      enabled: true
    - host: localhost
      port: 8082
      weight: 1
      enabled: true

---
# Production profile
spring:
  config:
    activate:
      on-profile: prod

logging:
  level:
    com.example.l4lb: INFO
    root: WARN

loadbalancer:
  port: 80
  connection:
    max-connections: 10000
  health-check:
    interval-seconds: 15
  backends:
    - host: backend1.example.com
      port: 8080
      weight: 1
      enabled: true
    - host: backend2.example.com
      port: 8080
      weight: 1
      enabled: true
    - host: backend3.example.com
      port: 8080
      weight: 2
      enabled: true
