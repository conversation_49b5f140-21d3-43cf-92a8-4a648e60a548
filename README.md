# L4 Load Balancer

A high-performance Layer 4 (Transport Layer) Load Balancer implementation in Java using Netty and Spring Boot.

## Features

### Core Load Balancing
- **Multiple Algorithms**: Round Robin, Least Connections, Weighted Round Robin, IP Hash, Random
- **High Performance**: Built on Netty for maximum throughput and low latency
- **Connection Pooling**: Efficient connection management and reuse
- **TCP Proxy**: Pure Layer 4 load balancing without application layer inspection
- **Session Affinity**: IP Hash algorithm ensures client-to-backend consistency

### Health Checking
- **Automatic Health Monitoring**: Periodic TCP health checks for backend servers
- **Configurable Parameters**: Customizable intervals, timeouts, and retry logic
- **Graceful Failover**: Automatic removal of unhealthy backends from rotation
- **Health Recovery**: Automatic re-inclusion of recovered backends
- **Real-time Status**: Live health status tracking with consecutive failure counting

### Monitoring & Metrics
- **Prometheus Integration**: Built-in metrics export for monitoring
- **Spring Boot Actuator**: Health endpoints and application metrics
- **Connection Statistics**: Real-time tracking of connections, throughput, and errors
- **Health Status**: Backend server health and availability metrics
- **Performance Metrics**: Latency, success rates, and load distribution
- **Grafana Dashboards**: Pre-configured visualization dashboards

### Configuration
- **YAML Configuration**: Easy-to-use configuration format with validation
- **Multiple Profiles**: Development, testing, and production configurations
- **Environment Variables**: Support for containerized deployments
- **Spring Boot Configuration**: Leverages Spring Boot's configuration management

## Architecture Overview

The L4 Load Balancer is built with a modular architecture:

- **LoadBalancerApplication**: Main Spring Boot application entry point
- **LoadBalancerServer**: Netty-based TCP server handling client connections
- **LoadBalancer**: Core component managing backend selection and health tracking
- **LoadBalancingStrategy**: Pluggable algorithms (Round Robin, Least Connections, etc.)
- **HealthChecker**: Background service for monitoring backend server health
- **MetricsCollector**: Prometheus metrics collection and export
- **LoadBalancerHandler**: Netty channel handler for TCP proxying

## Quick Start

### Prerequisites
- Java 17 or higher
- Maven 3.9 or higher
- Docker (optional, for containerized deployment)

### Building the Project
```bash
git clone <repository-url>
cd l4-load-balancer
mvn clean compile
```

### Running the Load Balancer
```bash
# Using Maven (development)
mvn spring-boot:run

# Using JAR file
mvn clean package
java -jar target/l4-load-balancer-1.0.0-SNAPSHOT.jar

# With custom configuration
java -jar target/l4-load-balancer-1.0.0-SNAPSHOT.jar --spring.config.location=classpath:/custom-config.yml

# With specific profile
java -jar target/l4-load-balancer-1.0.0-SNAPSHOT.jar --spring.profiles.active=prod
```

### Configuration Example
```yaml
# Spring Boot Configuration
spring:
  application:
    name: l4-load-balancer
  profiles:
    active: dev

# Load Balancer Configuration
loadbalancer:
  port: 8080
  algorithm: ROUND_ROBIN
  backends:
    - host: backend1.example.com
      port: 8080
      weight: 1
      enabled: true
    - host: backend2.example.com
      port: 8080
      weight: 2
      enabled: true
  health-check:
    enabled: true
    interval-seconds: 30
    timeout-seconds: 5
    max-retries: 3
    path: /health
  connection:
    max-connections: 1000
    connection-timeout-ms: 5000
    idle-timeout-ms: 300000
    keep-alive: true
    tcp-no-delay: true
  metrics:
    enabled: true
    port: 9090
    path: /metrics

# Management endpoints (Spring Boot Actuator)
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  metrics:
    export:
      prometheus:
        enabled: true
```

## Load Balancing Algorithms

### Round Robin
Distributes requests evenly across all available backends in a circular manner.
```yaml
loadbalancer:
  algorithm: ROUND_ROBIN
```

### Least Connections
Routes requests to the backend server with the fewest active connections.
```yaml
loadbalancer:
  algorithm: LEAST_CONNECTIONS
```

### Weighted Round Robin
Distributes requests based on the weight assigned to each backend server.
```yaml
loadbalancer:
  algorithm: WEIGHTED_ROUND_ROBIN
  backends:
    - host: backend1.example.com
      port: 8080
      weight: 1
    - host: backend2.example.com
      port: 8080
      weight: 3  # Receives 3x more traffic
```

### IP Hash
Routes requests based on a hash of the client IP address, ensuring session affinity.
```yaml
loadbalancer:
  algorithm: IP_HASH
```

### Random
Randomly selects a backend server for each request.
```yaml
loadbalancer:
  algorithm: RANDOM
```

## Health Checking

The load balancer performs periodic health checks on backend servers:

```yaml
loadbalancer:
  health-check:
    enabled: true
    interval-seconds: 30      # Check every 30 seconds
    timeout-seconds: 5        # 5 second timeout per check
    max-retries: 3           # Retry 3 times before marking unhealthy
    path: /health            # HTTP health check path (future feature)
```

## Monitoring

### Metrics Endpoint
Access metrics at: `http://localhost:9090/metrics`

### Prometheus Integration
```yaml
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  metrics:
    export:
      prometheus:
        enabled: true
```

### Available Metrics
- `loadbalancer_connections_total` - Total connections processed
- `loadbalancer_connections_active` - Currently active connections
- `loadbalancer_connections_failed` - Failed connection attempts
- `loadbalancer_connection_duration` - Connection duration histogram

## Testing

### Unit Tests
```bash
mvn test
```

### Integration Tests
```bash
mvn verify
```

### Running Specific Tests
```bash
# Run a specific test class
mvn test -Dtest=LoadBalancerIntegrationTest

# Run a specific test method
mvn test -Dtest=LoadBalancerIntegrationTest#testLoadBalancerStartsSuccessfully
```

### Load Testing
Use tools like Apache Bench, wrk, or JMeter to test load balancer performance:

```bash
# Example with Apache Bench
ab -n 10000 -c 100 http://localhost:8080/

# Example with wrk
wrk -t12 -c400 -d30s http://localhost:8080/
```

### Test Configuration
The test configuration uses:
- Port 0 for random port assignment (avoids conflicts)
- Disabled health checks for faster test execution
- Disabled metrics collection for unit tests
- Debug logging for detailed test output

## Docker Support

### Building Docker Image
```bash
mvn clean package
docker build -t l4-load-balancer:latest .
```

### Running with Docker
```bash
docker run -p 8080:8080 -p 9090:9090 l4-load-balancer:latest
```

### Docker Compose
```yaml
version: '3.8'
services:
  load-balancer:
    image: l4-load-balancer:latest
    ports:
      - "8080:8080"
      - "9090:9090"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
    volumes:
      - ./config:/app/config
```

## Performance Tuning

### JVM Options
```bash
java -Xms2g -Xmx4g \
     -XX:+UseG1GC \
     -XX:MaxGCPauseMillis=200 \
     -XX:+UnlockExperimentalVMOptions \
     -XX:+UseZGC \
     -jar l4-load-balancer.jar
```

### Netty Tuning
```yaml
loadbalancer:
  connection:
    max-connections: 10000
    connection-timeout-ms: 3000
    idle-timeout-ms: 180000
    keep-alive: true
    tcp-no-delay: true
```

### OS Tuning (Linux)
```bash
# Increase file descriptor limits
echo "* soft nofile 65536" >> /etc/security/limits.conf
echo "* hard nofile 65536" >> /etc/security/limits.conf

# TCP tuning
echo 'net.core.somaxconn = 65536' >> /etc/sysctl.conf
echo 'net.ipv4.tcp_max_syn_backlog = 65536' >> /etc/sysctl.conf
sysctl -p
```

## Architecture

```
┌─────────────┐    ┌──────────────────┐    ┌─────────────┐
│   Client    │───▶│  L4 Load         │───▶│  Backend    │
│             │    │  Balancer        │    │  Server 1   │
└─────────────┘    │                  │    └─────────────┘
                   │  ┌─────────────┐ │    ┌─────────────┐
                   │  │ Netty       │ │───▶│  Backend    │
                   │  │ TCP Proxy   │ │    │  Server 2   │
                   │  └─────────────┘ │    └─────────────┘
                   │                  │    ┌─────────────┐
                   │  ┌─────────────┐ │───▶│  Backend    │
                   │  │ Health      │ │    │  Server 3   │
                   │  │ Checker     │ │    └─────────────┘
                   │  └─────────────┘ │
                   └──────────────────┘
```

## Troubleshooting

### Common Issues

#### Build Errors
If you encounter compilation errors, ensure you have:
- Java 17 or higher installed
- Maven 3.9 or higher installed
- All dependencies properly resolved (`mvn dependency:resolve`)

#### Port Conflicts
If the default port 8080 is in use:
```bash
# Use a different port
java -jar target/l4-load-balancer-1.0.0-SNAPSHOT.jar --loadbalancer.port=8090
```

#### Logging Issues
If you see logback configuration errors:
- Check that `logback-spring.xml` uses `SizeAndTimeBasedRollingPolicy`
- Ensure the logs directory exists and is writable

#### Test Failures
If tests fail due to port conflicts:
- Tests use port 0 (random assignment) to avoid conflicts
- Ensure no other services are binding to the same random ports

### Dependencies
The project uses:
- **Spring Boot 3.2.1** (Jakarta EE, not javax)
- **Netty 4.1.104.Final** for high-performance networking
- **Micrometer 1.12.1** for metrics collection
- **Jakarta Validation** for configuration validation

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Run `mvn clean verify` to ensure all tests pass
6. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
