package com.example.l4lb.metrics;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Gauge;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.atomic.AtomicLong;

/**
 * Collects and exposes metrics for the load balancer.
 */
@Component
public class MetricsCollector {

    private final MeterRegistry meterRegistry;

    // Connection metrics
    private final AtomicLong totalConnections = new AtomicLong(0);
    private final AtomicLong activeConnections = new AtomicLong(0);
    private final AtomicLong failedConnections = new AtomicLong(0);

    // Micrometer metrics
    private final Counter totalConnectionsCounter;
    private final Counter failedConnectionsCounter;
    private final Timer connectionDurationTimer;

    @Autowired
    public MetricsCollector(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;

        // Initialize counters
        this.totalConnectionsCounter = Counter.builder("loadbalancer.connections.total")
                .description("Total number of connections processed")
                .register(meterRegistry);

        this.failedConnectionsCounter = Counter.builder("loadbalancer.connections.failed")
                .description("Total number of failed connections")
                .register(meterRegistry);

        this.connectionDurationTimer = Timer.builder("loadbalancer.connection.duration")
                .description("Duration of connections")
                .register(meterRegistry);

        // Initialize gauges
        Gauge.builder("loadbalancer.connections.active", this, MetricsCollector::getActiveConnections)
                .description("Number of active connections")
                .register(meterRegistry);
    }

    /**
     * Increment total connections counter.
     */
    public void incrementTotalConnections() {
        totalConnections.incrementAndGet();
        totalConnectionsCounter.increment();
    }

    /**
     * Increment active connections counter.
     */
    public void incrementActiveConnections() {
        activeConnections.incrementAndGet();
    }

    /**
     * Decrement active connections counter.
     */
    public void decrementActiveConnections() {
        activeConnections.decrementAndGet();
    }

    /**
     * Increment failed connections counter.
     */
    public void incrementFailedConnections() {
        failedConnections.incrementAndGet();
        failedConnectionsCounter.increment();
    }

    /**
     * Record connection duration.
     */
    public void recordConnectionDuration(long durationMs) {
        connectionDurationTimer.record(durationMs, java.util.concurrent.TimeUnit.MILLISECONDS);
    }

    /**
     * Get total connections count.
     */
    public long getTotalConnections() {
        return totalConnections.get();
    }

    /**
     * Get active connections count.
     */
    public long getActiveConnections() {
        return activeConnections.get();
    }

    /**
     * Get failed connections count.
     */
    public long getFailedConnections() {
        return failedConnections.get();
    }

    /**
     * Get connection success rate.
     */
    public double getSuccessRate() {
        long total = getTotalConnections();
        if (total == 0) {
            return 1.0;
        }
        return (double) (total - getFailedConnections()) / total;
    }

    /**
     * Get metrics summary.
     */
    public MetricsSummary getMetricsSummary() {
        return new MetricsSummary(
                getTotalConnections(),
                getActiveConnections(),
                getFailedConnections(),
                getSuccessRate()
        );
    }

    /**
     * Reset all metrics (useful for testing).
     */
    public void reset() {
        totalConnections.set(0);
        activeConnections.set(0);
        failedConnections.set(0);
    }

    /**
     * Metrics summary data class.
     */
    public static class MetricsSummary {
        private final long totalConnections;
        private final long activeConnections;
        private final long failedConnections;
        private final double successRate;

        public MetricsSummary(long totalConnections, long activeConnections, 
                            long failedConnections, double successRate) {
            this.totalConnections = totalConnections;
            this.activeConnections = activeConnections;
            this.failedConnections = failedConnections;
            this.successRate = successRate;
        }

        public long getTotalConnections() { return totalConnections; }
        public long getActiveConnections() { return activeConnections; }
        public long getFailedConnections() { return failedConnections; }
        public double getSuccessRate() { return successRate; }

        @Override
        public String toString() {
            return "MetricsSummary{" +
                    "totalConnections=" + totalConnections +
                    ", activeConnections=" + activeConnections +
                    ", failedConnections=" + failedConnections +
                    ", successRate=" + String.format("%.2f%%", successRate * 100) +
                    '}';
        }
    }
}
