package com.example.l4lb.handler;

import com.example.l4lb.config.LoadBalancerConfig;
import com.example.l4lb.loadbalancing.LoadBalancer;
import com.example.l4lb.metrics.MetricsCollector;
import com.example.l4lb.model.BackendServerInfo;
import io.netty.bootstrap.Bootstrap;
import io.netty.buffer.Unpooled;
import io.netty.channel.*;
import io.netty.channel.socket.nio.NioSocketChannel;
import io.netty.handler.timeout.IdleState;
import io.netty.handler.timeout.IdleStateEvent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.InetSocketAddress;

/**
 * Main handler for processing incoming connections and forwarding them to backend servers.
 * This handler implements the core L4 load balancing logic.
 */
@ChannelHandler.Sharable
public class LoadBalancerHandler extends ChannelInboundHandlerAdapter {

    private static final Logger logger = LoggerFactory.getLogger(LoadBalancerHandler.class);

    private final LoadBalancer loadBalancer;
    private final MetricsCollector metricsCollector;
    private final LoadBalancerConfig config;

    // Connection state
    private Channel backendChannel;
    private BackendServerInfo selectedBackend;
    private String clientAddress;

    public LoadBalancerHandler(LoadBalancer loadBalancer, 
                              MetricsCollector metricsCollector,
                              LoadBalancerConfig config) {
        this.loadBalancer = loadBalancer;
        this.metricsCollector = metricsCollector;
        this.config = config;
    }

    @Override
    public void channelActive(ChannelHandlerContext ctx) throws Exception {
        // Get client address
        InetSocketAddress clientSocketAddress = (InetSocketAddress) ctx.channel().remoteAddress();
        clientAddress = clientSocketAddress.getAddress().getHostAddress();
        
        logger.debug("New connection from client: {}", clientAddress);
        metricsCollector.incrementTotalConnections();
        metricsCollector.incrementActiveConnections();

        // Select backend server
        selectedBackend = loadBalancer.selectBackend(clientAddress);
        if (selectedBackend == null) {
            logger.warn("No healthy backend available for client: {}", clientAddress);
            metricsCollector.incrementFailedConnections();
            ctx.close();
            return;
        }

        // Connect to backend server
        connectToBackend(ctx);
    }

    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception {
        if (backendChannel != null && backendChannel.isActive()) {
            // Forward data to backend
            backendChannel.writeAndFlush(msg).addListener(future -> {
                if (!future.isSuccess()) {
                    logger.warn("Failed to write to backend {}:{}", 
                               selectedBackend.getHost(), selectedBackend.getPort(), future.cause());
                    ctx.close();
                }
            });
        } else {
            // Backend not available, close client connection
            logger.warn("Backend connection not available, closing client connection");
            ctx.close();
        }
    }

    @Override
    public void channelInactive(ChannelHandlerContext ctx) throws Exception {
        logger.debug("Client connection closed: {}", clientAddress);
        
        // Close backend connection
        if (backendChannel != null) {
            backendChannel.close();
        }
        
        // Release backend
        if (selectedBackend != null) {
            loadBalancer.releaseBackend(selectedBackend);
        }
        
        metricsCollector.decrementActiveConnections();
    }

    @Override
    public void userEventTriggered(ChannelHandlerContext ctx, Object evt) throws Exception {
        if (evt instanceof IdleStateEvent) {
            IdleStateEvent idleEvent = (IdleStateEvent) evt;
            if (idleEvent.state() == IdleState.ALL_IDLE) {
                logger.debug("Connection idle timeout for client: {}", clientAddress);
                ctx.close();
            }
        }
        super.userEventTriggered(ctx, evt);
    }

    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
        logger.warn("Exception in client connection from {}: {}", clientAddress, cause.getMessage());
        
        if (selectedBackend != null) {
            selectedBackend.incrementFailedConnections();
        }
        
        metricsCollector.incrementFailedConnections();
        ctx.close();
    }

    /**
     * Connect to the selected backend server.
     */
    private void connectToBackend(ChannelHandlerContext clientCtx) {
        Bootstrap bootstrap = new Bootstrap();
        bootstrap.group(clientCtx.channel().eventLoop())
                .channel(NioSocketChannel.class)
                .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, (int) config.getConnection().getConnectionTimeoutMs())
                .option(ChannelOption.SO_KEEPALIVE, config.getConnection().isKeepAlive())
                .option(ChannelOption.TCP_NODELAY, config.getConnection().isTcpNoDelay())
                .handler(new BackendChannelInitializer(clientCtx.channel()));

        // Connect to backend
        ChannelFuture connectFuture = bootstrap.connect(selectedBackend.getHost(), selectedBackend.getPort());
        
        connectFuture.addListener(future -> {
            if (future.isSuccess()) {
                backendChannel = connectFuture.channel();
                logger.debug("Connected to backend {}:{} for client: {}", 
                           selectedBackend.getHost(), selectedBackend.getPort(), clientAddress);
            } else {
                logger.warn("Failed to connect to backend {}:{} for client: {}", 
                           selectedBackend.getHost(), selectedBackend.getPort(), clientAddress, future.cause());
                
                selectedBackend.incrementFailedConnections();
                metricsCollector.incrementFailedConnections();
                clientCtx.close();
            }
        });
    }

    /**
     * Channel initializer for backend connections.
     */
    private class BackendChannelInitializer extends ChannelInitializer<Channel> {
        private final Channel clientChannel;

        public BackendChannelInitializer(Channel clientChannel) {
            this.clientChannel = clientChannel;
        }

        @Override
        protected void initChannel(Channel ch) throws Exception {
            ch.pipeline().addLast(new BackendChannelHandler(clientChannel, selectedBackend));
        }
    }

    /**
     * Handler for backend channel events.
     */
    private class BackendChannelHandler extends ChannelInboundHandlerAdapter {
        private final Channel clientChannel;
        private final BackendServerInfo backend;

        public BackendChannelHandler(Channel clientChannel, BackendServerInfo backend) {
            this.clientChannel = clientChannel;
            this.backend = backend;
        }

        @Override
        public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception {
            if (clientChannel.isActive()) {
                // Forward data to client
                clientChannel.writeAndFlush(msg).addListener(future -> {
                    if (!future.isSuccess()) {
                        logger.warn("Failed to write to client from backend {}:{}", 
                                   backend.getHost(), backend.getPort(), future.cause());
                        ctx.close();
                    }
                });
            } else {
                // Client connection closed, close backend connection
                ctx.close();
            }
        }

        @Override
        public void channelInactive(ChannelHandlerContext ctx) throws Exception {
            logger.debug("Backend connection closed: {}:{}", backend.getHost(), backend.getPort());
            
            // Close client connection if still active
            if (clientChannel.isActive()) {
                clientChannel.close();
            }
        }

        @Override
        public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
            logger.warn("Exception in backend connection {}:{}: {}", 
                       backend.getHost(), backend.getPort(), cause.getMessage());
            
            backend.incrementFailedConnections();
            ctx.close();
        }
    }
}
