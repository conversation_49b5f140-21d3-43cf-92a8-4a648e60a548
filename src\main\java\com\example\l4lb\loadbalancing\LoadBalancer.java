package com.example.l4lb.loadbalancing;

import com.example.l4lb.config.LoadBalancerConfig;
import com.example.l4lb.healthcheck.HealthChecker;
import com.example.l4lb.model.BackendServerInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;

/**
 * Main load balancer component that manages backend servers and routing decisions.
 */
@Component
public class LoadBalancer {

    private static final Logger logger = LoggerFactory.getLogger(LoadBalancer.class);

    private final LoadBalancerConfig config;
    private final HealthChecker healthChecker;
    private final LoadBalancingStrategy strategy;

    private final List<BackendServerInfo> allBackends = new CopyOnWriteArrayList<>();
    private volatile List<BackendServerInfo> healthyBackends = new CopyOnWriteArrayList<>();

    @Autowired
    public LoadBalancer(LoadBalancerConfig config, HealthChecker healthChecker) {
        this.config = config;
        this.healthChecker = healthChecker;
        this.strategy = createLoadBalancingStrategy(config.getAlgorithm());
    }

    @PostConstruct
    public void initialize() {
        logger.info("Initializing Load Balancer with algorithm: {}", config.getAlgorithm());
        
        // Initialize backend servers
        for (LoadBalancerConfig.BackendServer backendConfig : config.getBackends()) {
            BackendServerInfo backend = new BackendServerInfo(
                    backendConfig.getHost(),
                    backendConfig.getPort(),
                    backendConfig.getWeight(),
                    backendConfig.isEnabled()
            );
            allBackends.add(backend);
            
            if (backend.isEnabled()) {
                healthyBackends.add(backend);
            }
        }

        // Start health checking
        if (config.getHealthCheck().isEnabled()) {
            healthChecker.startHealthChecking(allBackends, this::updateHealthyBackends);
        }

        logger.info("Load Balancer initialized with {} backends ({} healthy)", 
                   allBackends.size(), healthyBackends.size());
    }

    /**
     * Select the next backend server for a new connection.
     */
    public BackendServerInfo selectBackend(String clientAddress) {
        List<BackendServerInfo> availableBackends = getHealthyBackends();
        
        if (availableBackends.isEmpty()) {
            logger.warn("No healthy backends available for client: {}", clientAddress);
            return null;
        }

        BackendServerInfo selected = strategy.selectBackend(availableBackends, clientAddress);
        
        if (selected != null) {
            selected.incrementActiveConnections();
            logger.debug("Selected backend {}:{} for client: {}", 
                        selected.getHost(), selected.getPort(), clientAddress);
        }

        return selected;
    }

    /**
     * Notify that a connection to a backend has been closed.
     */
    public void releaseBackend(BackendServerInfo backend) {
        if (backend != null) {
            backend.decrementActiveConnections();
            logger.debug("Released connection to backend {}:{}", backend.getHost(), backend.getPort());
        }
    }

    /**
     * Get the list of healthy backends.
     */
    public List<BackendServerInfo> getHealthyBackends() {
        return healthyBackends;
    }

    /**
     * Get the list of all backends.
     */
    public List<BackendServerInfo> getAllBackends() {
        return allBackends;
    }

    /**
     * Get the number of active (healthy) backends.
     */
    public int getActiveBackendCount() {
        return healthyBackends.size();
    }

    /**
     * Update the list of healthy backends based on health check results.
     */
    private void updateHealthyBackends(List<BackendServerInfo> newHealthyBackends) {
        List<BackendServerInfo> previousHealthy = this.healthyBackends;
        this.healthyBackends = new CopyOnWriteArrayList<>(newHealthyBackends);
        
        // Log changes
        if (previousHealthy.size() != newHealthyBackends.size()) {
            logger.info("Healthy backends updated: {} -> {} backends", 
                       previousHealthy.size(), newHealthyBackends.size());
            
            // Log newly healthy backends
            newHealthyBackends.stream()
                    .filter(backend -> !previousHealthy.contains(backend))
                    .forEach(backend -> logger.info("Backend {}:{} is now healthy", 
                                                   backend.getHost(), backend.getPort()));
            
            // Log newly unhealthy backends
            previousHealthy.stream()
                    .filter(backend -> !newHealthyBackends.contains(backend))
                    .forEach(backend -> logger.warn("Backend {}:{} is now unhealthy", 
                                                   backend.getHost(), backend.getPort()));
        }
    }

    /**
     * Create the appropriate load balancing strategy based on configuration.
     */
    private LoadBalancingStrategy createLoadBalancingStrategy(LoadBalancerConfig.LoadBalancingAlgorithm algorithm) {
        switch (algorithm) {
            case ROUND_ROBIN:
                return new RoundRobinStrategy();
            case LEAST_CONNECTIONS:
                return new LeastConnectionsStrategy();
            case WEIGHTED_ROUND_ROBIN:
                return new WeightedRoundRobinStrategy();
            case IP_HASH:
                return new IpHashStrategy();
            case RANDOM:
                return new RandomStrategy();
            default:
                logger.warn("Unknown load balancing algorithm: {}, falling back to ROUND_ROBIN", algorithm);
                return new RoundRobinStrategy();
        }
    }

    /**
     * Get load balancer statistics.
     */
    public LoadBalancerStats getStats() {
        long totalConnections = allBackends.stream()
                .mapToLong(BackendServerInfo::getTotalConnections)
                .sum();
        
        long activeConnections = allBackends.stream()
                .mapToLong(BackendServerInfo::getActiveConnections)
                .sum();

        return new LoadBalancerStats(
                config.getAlgorithm(),
                allBackends.size(),
                healthyBackends.size(),
                totalConnections,
                activeConnections,
                allBackends.stream()
                        .collect(Collectors.toMap(
                                backend -> backend.getHost() + ":" + backend.getPort(),
                                BackendServerInfo::getActiveConnections
                        ))
        );
    }

    /**
     * Load balancer statistics data class.
     */
    public static class LoadBalancerStats {
        private final LoadBalancerConfig.LoadBalancingAlgorithm algorithm;
        private final int totalBackends;
        private final int healthyBackends;
        private final long totalConnections;
        private final long activeConnections;
        private final java.util.Map<String, Long> backendConnections;

        public LoadBalancerStats(LoadBalancerConfig.LoadBalancingAlgorithm algorithm,
                               int totalBackends, int healthyBackends,
                               long totalConnections, long activeConnections,
                               java.util.Map<String, Long> backendConnections) {
            this.algorithm = algorithm;
            this.totalBackends = totalBackends;
            this.healthyBackends = healthyBackends;
            this.totalConnections = totalConnections;
            this.activeConnections = activeConnections;
            this.backendConnections = backendConnections;
        }

        // Getters
        public LoadBalancerConfig.LoadBalancingAlgorithm getAlgorithm() { return algorithm; }
        public int getTotalBackends() { return totalBackends; }
        public int getHealthyBackends() { return healthyBackends; }
        public long getTotalConnections() { return totalConnections; }
        public long getActiveConnections() { return activeConnections; }
        public java.util.Map<String, Long> getBackendConnections() { return backendConnections; }

        @Override
        public String toString() {
            return "LoadBalancerStats{" +
                    "algorithm=" + algorithm +
                    ", totalBackends=" + totalBackends +
                    ", healthyBackends=" + healthyBackends +
                    ", totalConnections=" + totalConnections +
                    ", activeConnections=" + activeConnections +
                    ", backendConnections=" + backendConnections +
                    '}';
        }
    }
}
