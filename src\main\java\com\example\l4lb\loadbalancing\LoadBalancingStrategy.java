package com.example.l4lb.loadbalancing;

import com.example.l4lb.model.BackendServerInfo;

import java.util.List;

/**
 * Interface for load balancing strategies.
 */
public interface LoadBalancingStrategy {
    
    /**
     * Select a backend server from the list of available backends.
     * 
     * @param backends List of healthy backend servers
     * @param clientAddress Client IP address (for IP-based algorithms)
     * @return Selected backend server, or null if none available
     */
    BackendServerInfo selectBackend(List<BackendServerInfo> backends, String clientAddress);
}
