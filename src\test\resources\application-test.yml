# Test configuration for L4 Load Balancer

spring:
  application:
    name: l4-load-balancer-test

loadbalancer:
  port: 0  # Use random available port for testing
  algorithm: ROUND_ROBIN
  backends:
    - host: localhost
      port: 8081
      weight: 1
      enabled: true
    - host: localhost
      port: 8082
      weight: 1
      enabled: true
    - host: localhost
      port: 8083
      weight: 1
      enabled: true
  
  health-check:
    enabled: false  # Disable for unit tests
    interval-seconds: 5
    timeout-seconds: 2
    max-retries: 1
  
  connection:
    max-connections: 100
    connection-timeout-ms: 1000
    idle-timeout-ms: 30000
    keep-alive: true
    tcp-no-delay: true
  
  metrics:
    enabled: false  # Disable for unit tests
    port: 0
    path: /metrics

management:
  endpoints:
    web:
      exposure:
        include: health,info
  endpoint:
    health:
      show-details: always

logging:
  level:
    com.example.l4lb: DEBUG
    io.netty: WARN
    org.springframework: WARN
  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
