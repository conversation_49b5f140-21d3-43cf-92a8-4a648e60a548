package com.example.l4lb.loadbalancing;

import com.example.l4lb.model.BackendServerInfo;

import java.util.List;

/**
 * Least connections load balancing strategy.
 * Routes requests to the backend server with the fewest active connections.
 */
public class LeastConnectionsStrategy implements LoadBalancingStrategy {

    @Override
    public BackendServerInfo selectBackend(List<BackendServerInfo> backends, String clientAddress) {
        if (backends == null || backends.isEmpty()) {
            return null;
        }

        BackendServerInfo selected = backends.get(0);
        long minConnections = selected.getActiveConnections();

        for (BackendServerInfo backend : backends) {
            long connections = backend.getActiveConnections();
            if (connections < minConnections) {
                minConnections = connections;
                selected = backend;
            }
        }

        return selected;
    }
}
