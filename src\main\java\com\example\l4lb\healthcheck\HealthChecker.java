package com.example.l4lb.healthcheck;

import com.example.l4lb.config.LoadBalancerConfig;
import com.example.l4lb.model.BackendServerInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.Socket;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * Health checker for backend servers.
 * Performs periodic health checks and updates the health status of backend servers.
 */
@Component
public class HealthChecker {

    private static final Logger logger = LoggerFactory.getLogger(HealthChecker.class);

    private final LoadBalancerConfig config;
    private final ScheduledExecutorService scheduler;

    @Autowired
    public HealthChecker(LoadBalancerConfig config) {
        this.config = config;
        this.scheduler = Executors.newScheduledThreadPool(
                Math.max(1, config.getBackends().size()),
                r -> {
                    Thread t = new Thread(r, "health-checker");
                    t.setDaemon(true);
                    return t;
                }
        );
    }

    /**
     * Start health checking for the given backend servers.
     */
    public void startHealthChecking(List<BackendServerInfo> backends, 
                                   Consumer<List<BackendServerInfo>> healthyBackendsCallback) {
        if (!config.getHealthCheck().isEnabled()) {
            logger.info("Health checking is disabled");
            return;
        }

        logger.info("Starting health checks for {} backends", backends.size());

        // Schedule periodic health checks
        scheduler.scheduleWithFixedDelay(
                () -> performHealthChecks(backends, healthyBackendsCallback),
                0, // Initial delay
                config.getHealthCheck().getIntervalSeconds(),
                TimeUnit.SECONDS
        );
    }

    /**
     * Stop health checking.
     */
    public void stop() {
        logger.info("Stopping health checker");
        scheduler.shutdown();
        try {
            if (!scheduler.awaitTermination(10, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            scheduler.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }

    /**
     * Perform health checks for all backend servers.
     */
    private void performHealthChecks(List<BackendServerInfo> backends,
                                   Consumer<List<BackendServerInfo>> healthyBackendsCallback) {
        logger.debug("Performing health checks for {} backends", backends.size());

        // Check each backend in parallel
        backends.parallelStream().forEach(this::checkBackendHealth);

        // Get list of healthy backends
        List<BackendServerInfo> healthyBackends = backends.stream()
                .filter(BackendServerInfo::isHealthy)
                .collect(Collectors.toList());

        // Notify callback with updated healthy backends
        healthyBackendsCallback.accept(healthyBackends);

        logger.debug("Health check completed: {}/{} backends healthy", 
                    healthyBackends.size(), backends.size());
    }

    /**
     * Check the health of a single backend server.
     */
    private void checkBackendHealth(BackendServerInfo backend) {
        if (!backend.isEnabled()) {
            backend.setHealthy(false);
            return;
        }

        boolean isHealthy = performTcpHealthCheck(backend);
        backend.setHealthy(isHealthy);

        if (isHealthy) {
            logger.debug("Backend {}:{} is healthy", backend.getHost(), backend.getPort());
        } else {
            logger.warn("Backend {}:{} is unhealthy (consecutive failures: {})", 
                       backend.getHost(), backend.getPort(), backend.getConsecutiveFailures());
        }
    }

    /**
     * Perform TCP health check by attempting to connect to the backend server.
     */
    private boolean performTcpHealthCheck(BackendServerInfo backend) {
        int maxRetries = config.getHealthCheck().getMaxRetries();
        long timeoutMs = config.getHealthCheck().getTimeoutSeconds() * 1000;

        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            try (Socket socket = new Socket()) {
                socket.connect(
                        new InetSocketAddress(backend.getHost(), backend.getPort()),
                        (int) timeoutMs
                );
                
                // Connection successful
                logger.debug("Health check successful for {}:{} (attempt {}/{})", 
                           backend.getHost(), backend.getPort(), attempt, maxRetries);
                return true;

            } catch (IOException e) {
                logger.debug("Health check failed for {}:{} (attempt {}/{}): {}", 
                           backend.getHost(), backend.getPort(), attempt, maxRetries, e.getMessage());
                
                if (attempt < maxRetries) {
                    // Wait before retry
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
        }

        return false;
    }

    /**
     * Perform HTTP health check (for future enhancement).
     */
    private boolean performHttpHealthCheck(BackendServerInfo backend) {
        // TODO: Implement HTTP health check
        // This would send an HTTP GET request to the health check path
        // and verify the response status code
        return performTcpHealthCheck(backend);
    }

    /**
     * Get health check statistics.
     */
    public HealthCheckStats getStats(List<BackendServerInfo> backends) {
        long totalBackends = backends.size();
        long healthyBackends = backends.stream()
                .mapToLong(backend -> backend.isHealthy() ? 1 : 0)
                .sum();
        long enabledBackends = backends.stream()
                .mapToLong(backend -> backend.isEnabled() ? 1 : 0)
                .sum();

        return new HealthCheckStats(
                config.getHealthCheck().isEnabled(),
                totalBackends,
                enabledBackends,
                healthyBackends,
                config.getHealthCheck().getIntervalSeconds(),
                config.getHealthCheck().getTimeoutSeconds(),
                config.getHealthCheck().getMaxRetries()
        );
    }

    /**
     * Health check statistics data class.
     */
    public static class HealthCheckStats {
        private final boolean enabled;
        private final long totalBackends;
        private final long enabledBackends;
        private final long healthyBackends;
        private final long intervalSeconds;
        private final long timeoutSeconds;
        private final int maxRetries;

        public HealthCheckStats(boolean enabled, long totalBackends, long enabledBackends,
                              long healthyBackends, long intervalSeconds, long timeoutSeconds, int maxRetries) {
            this.enabled = enabled;
            this.totalBackends = totalBackends;
            this.enabledBackends = enabledBackends;
            this.healthyBackends = healthyBackends;
            this.intervalSeconds = intervalSeconds;
            this.timeoutSeconds = timeoutSeconds;
            this.maxRetries = maxRetries;
        }

        // Getters
        public boolean isEnabled() { return enabled; }
        public long getTotalBackends() { return totalBackends; }
        public long getEnabledBackends() { return enabledBackends; }
        public long getHealthyBackends() { return healthyBackends; }
        public long getIntervalSeconds() { return intervalSeconds; }
        public long getTimeoutSeconds() { return timeoutSeconds; }
        public int getMaxRetries() { return maxRetries; }

        @Override
        public String toString() {
            return "HealthCheckStats{" +
                    "enabled=" + enabled +
                    ", totalBackends=" + totalBackends +
                    ", enabledBackends=" + enabledBackends +
                    ", healthyBackends=" + healthyBackends +
                    ", intervalSeconds=" + intervalSeconds +
                    ", timeoutSeconds=" + timeoutSeconds +
                    ", maxRetries=" + maxRetries +
                    '}';
        }
    }
}
