package com.example.l4lb.loadbalancing;

import com.example.l4lb.model.BackendServerInfo;

import java.util.List;

/**
 * IP hash load balancing strategy.
 * Routes requests based on a hash of the client IP address,
 * ensuring that the same client always goes to the same backend server.
 */
public class IpHashStrategy implements LoadBalancingStrategy {

    @Override
    public BackendServerInfo selectBackend(List<BackendServerInfo> backends, String clientAddress) {
        if (backends == null || backends.isEmpty()) {
            return null;
        }

        if (clientAddress == null || clientAddress.isEmpty()) {
            // Fallback to first backend if no client address
            return backends.get(0);
        }

        // Calculate hash of client IP address
        int hash = Math.abs(clientAddress.hashCode());
        int index = hash % backends.size();

        return backends.get(index);
    }
}
