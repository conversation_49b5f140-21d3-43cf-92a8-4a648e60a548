package com.example.l4lb.loadbalancing;

import com.example.l4lb.model.BackendServerInfo;

import java.util.List;
import java.util.concurrent.ThreadLocalRandom;

/**
 * Random load balancing strategy.
 * Randomly selects a backend server for each request.
 */
public class RandomStrategy implements LoadBalancingStrategy {

    @Override
    public BackendServerInfo selectBackend(List<BackendServerInfo> backends, String clientAddress) {
        if (backends == null || backends.isEmpty()) {
            return null;
        }

        int randomIndex = ThreadLocalRandom.current().nextInt(backends.size());
        return backends.get(randomIndex);
    }
}
