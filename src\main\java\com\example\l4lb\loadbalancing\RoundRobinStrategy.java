package com.example.l4lb.loadbalancing;

import com.example.l4lb.model.BackendServerInfo;

import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Round-robin load balancing strategy.
 * Distributes requests evenly across all available backends in a circular manner.
 */
public class RoundRobinStrategy implements LoadBalancingStrategy {

    private final AtomicInteger currentIndex = new AtomicInteger(0);

    @Override
    public BackendServerInfo selectBackend(List<BackendServerInfo> backends, String clientAddress) {
        if (backends == null || backends.isEmpty()) {
            return null;
        }

        int index = currentIndex.getAndIncrement() % backends.size();
        return backends.get(index);
    }
}
