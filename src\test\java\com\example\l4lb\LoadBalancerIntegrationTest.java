package com.example.l4lb;

import com.example.l4lb.config.LoadBalancerConfig;
import com.example.l4lb.loadbalancing.LoadBalancer;
import com.example.l4lb.metrics.MetricsCollector;
import com.example.l4lb.model.BackendServerInfo;
import com.example.l4lb.server.LoadBalancerServer;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.io.IOException;
import java.net.ServerSocket;
import java.net.Socket;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Integration tests for the L4 Load Balancer.
 */
@SpringBootTest
@ActiveProfiles("test")
public class LoadBalancerIntegrationTest {

    private LoadBalancerConfig config;
    private LoadBalancer loadBalancer;
    private MetricsCollector metricsCollector;
    private LoadBalancerServer server;
    
    private List<MockBackendServer> mockBackends;
    private ExecutorService executorService;

    @BeforeEach
    void setUp() {
        executorService = Executors.newCachedThreadPool();
        mockBackends = new ArrayList<>();
        
        // Create test configuration
        config = createTestConfig();
        
        // Start mock backend servers
        startMockBackends();
    }

    @Test
    void testLoadBalancerStartsSuccessfully() throws Exception {
        // Given
        loadBalancer = new LoadBalancer(config, null);
        metricsCollector = new MetricsCollector(null);
        server = new LoadBalancerServer(config, loadBalancer, metricsCollector);

        // When
        CompletableFuture<Void> startFuture = CompletableFuture.runAsync(() -> {
            try {
                server.start();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        });

        // Wait a bit for server to start
        Thread.sleep(1000);

        // Then
        assertTrue(server.isRunning());
        
        // Cleanup
        server.stop();
        startFuture.cancel(true);
    }

    @Test
    void testRoundRobinLoadBalancing() {
        // Given
        config.setAlgorithm(LoadBalancerConfig.LoadBalancingAlgorithm.ROUND_ROBIN);
        loadBalancer = new LoadBalancer(config, null);
        
        List<BackendServerInfo> backends = createTestBackends();

        // When & Then
        BackendServerInfo first = loadBalancer.selectBackend("***********");
        BackendServerInfo second = loadBalancer.selectBackend("***********");
        BackendServerInfo third = loadBalancer.selectBackend("192.168.1.3");
        BackendServerInfo fourth = loadBalancer.selectBackend("192.168.1.4");

        assertNotNull(first);
        assertNotNull(second);
        assertNotNull(third);
        assertNotNull(fourth);

        // Should cycle through backends
        assertNotEquals(first, second);
        assertNotEquals(second, third);
        assertEquals(first, fourth); // Should cycle back to first
    }

    @Test
    void testLeastConnectionsLoadBalancing() {
        // Given
        config.setAlgorithm(LoadBalancerConfig.LoadBalancingAlgorithm.LEAST_CONNECTIONS);
        loadBalancer = new LoadBalancer(config, null);
        
        List<BackendServerInfo> backends = createTestBackends();

        // When
        BackendServerInfo first = loadBalancer.selectBackend("***********");
        BackendServerInfo second = loadBalancer.selectBackend("***********");

        // Then
        assertNotNull(first);
        assertNotNull(second);
        
        // First backend should have 1 connection, second should have 0
        // So second selection should go to a different backend
        assertTrue(first.getActiveConnections() > 0);
    }

    @Test
    void testIpHashLoadBalancing() {
        // Given
        config.setAlgorithm(LoadBalancerConfig.LoadBalancingAlgorithm.IP_HASH);
        loadBalancer = new LoadBalancer(config, null);

        // When
        BackendServerInfo first = loadBalancer.selectBackend("***********");
        BackendServerInfo second = loadBalancer.selectBackend("***********"); // Same IP
        BackendServerInfo third = loadBalancer.selectBackend("***********"); // Different IP

        // Then
        assertNotNull(first);
        assertNotNull(second);
        assertNotNull(third);
        
        // Same IP should go to same backend
        assertEquals(first, second);
    }

    @Test
    void testMetricsCollection() {
        // Given
        metricsCollector = new MetricsCollector(null);

        // When
        metricsCollector.incrementTotalConnections();
        metricsCollector.incrementActiveConnections();
        metricsCollector.incrementFailedConnections();

        // Then
        assertEquals(1, metricsCollector.getTotalConnections());
        assertEquals(1, metricsCollector.getActiveConnections());
        assertEquals(1, metricsCollector.getFailedConnections());
        assertEquals(0.0, metricsCollector.getSuccessRate(), 0.01);
    }

    @Test
    void testBackendServerInfo() {
        // Given
        BackendServerInfo backend = new BackendServerInfo("localhost", 8080, 1, true);

        // When
        backend.incrementActiveConnections();
        backend.incrementActiveConnections();
        backend.decrementActiveConnections();
        backend.incrementFailedConnections();

        // Then
        assertEquals(2, backend.getTotalConnections());
        assertEquals(1, backend.getActiveConnections());
        assertEquals(1, backend.getFailedConnections());
        assertTrue(backend.isHealthy());
        assertTrue(backend.isEnabled());
    }

    @Test
    void testConcurrentConnections() throws Exception {
        // Given
        loadBalancer = new LoadBalancer(config, null);
        int numberOfThreads = 10;
        int connectionsPerThread = 100;
        
        ExecutorService executor = Executors.newFixedThreadPool(numberOfThreads);
        List<CompletableFuture<Void>> futures = new ArrayList<>();

        // When
        for (int i = 0; i < numberOfThreads; i++) {
            final int threadId = i;
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                for (int j = 0; j < connectionsPerThread; j++) {
                    String clientIp = "192.168.1." + (threadId * 100 + j);
                    BackendServerInfo backend = loadBalancer.selectBackend(clientIp);
                    assertNotNull(backend);
                    loadBalancer.releaseBackend(backend);
                }
            }, executor);
            futures.add(future);
        }

        // Wait for all threads to complete
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).get(30, TimeUnit.SECONDS);

        // Then
        List<BackendServerInfo> backends = loadBalancer.getAllBackends();
        long totalConnections = backends.stream()
                .mapToLong(BackendServerInfo::getTotalConnections)
                .sum();
        
        assertEquals(numberOfThreads * connectionsPerThread, totalConnections);
        
        executor.shutdown();
    }

    private LoadBalancerConfig createTestConfig() {
        LoadBalancerConfig config = new LoadBalancerConfig();
        config.setPort(findAvailablePort());
        config.setAlgorithm(LoadBalancerConfig.LoadBalancingAlgorithm.ROUND_ROBIN);
        
        List<LoadBalancerConfig.BackendServer> backends = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            LoadBalancerConfig.BackendServer backend = new LoadBalancerConfig.BackendServer();
            backend.setHost("localhost");
            backend.setPort(8081 + i);
            backend.setWeight(1);
            backend.setEnabled(true);
            backends.add(backend);
        }
        config.setBackends(backends);
        
        // Disable health checks for testing
        LoadBalancerConfig.HealthCheckConfig healthCheck = new LoadBalancerConfig.HealthCheckConfig();
        healthCheck.setEnabled(false);
        config.setHealthCheck(healthCheck);
        
        return config;
    }

    private List<BackendServerInfo> createTestBackends() {
        List<BackendServerInfo> backends = new ArrayList<>();
        for (LoadBalancerConfig.BackendServer backendConfig : config.getBackends()) {
            backends.add(new BackendServerInfo(
                    backendConfig.getHost(),
                    backendConfig.getPort(),
                    backendConfig.getWeight(),
                    backendConfig.isEnabled()
            ));
        }
        return backends;
    }

    private void startMockBackends() {
        for (LoadBalancerConfig.BackendServer backendConfig : config.getBackends()) {
            MockBackendServer mockServer = new MockBackendServer(backendConfig.getPort());
            mockBackends.add(mockServer);
            executorService.submit(mockServer);
        }
    }

    private int findAvailablePort() {
        try (ServerSocket socket = new ServerSocket(0)) {
            return socket.getLocalPort();
        } catch (IOException e) {
            return 8080; // Fallback
        }
    }

    /**
     * Mock backend server for testing.
     */
    private static class MockBackendServer implements Runnable {
        private final int port;
        private volatile boolean running = true;

        public MockBackendServer(int port) {
            this.port = port;
        }

        @Override
        public void run() {
            try (ServerSocket serverSocket = new ServerSocket(port)) {
                while (running) {
                    try {
                        Socket clientSocket = serverSocket.accept();
                        // Echo server - just close the connection
                        clientSocket.close();
                    } catch (IOException e) {
                        if (running) {
                            System.err.println("Error in mock backend server: " + e.getMessage());
                        }
                    }
                }
            } catch (IOException e) {
                System.err.println("Failed to start mock backend server on port " + port + ": " + e.getMessage());
            }
        }

        public void stop() {
            running = false;
        }
    }
}
